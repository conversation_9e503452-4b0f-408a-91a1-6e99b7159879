<!--
 * @Author: czw
 * @Date: 2022-11-03 17:55:45
 * @LastEditors: czw
 * @LastEditTime: 2022-11-04 10:11:20
 * @FilePath: \kdsp_vue_clear\src\systemView\views\menus\index.vue
 * @Description:
 *
 * Copyright (c) 2022 by czw/bysc, All Rights Reserved.
-->
<!--  -->
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="tenant/tenant-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="租户名称">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.tenantName"
                  size="small"
                  placeholder="请输入租户名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="域名">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.domainName"
                  size="small"
                  placeholder="请输入域名"
                ></el-input>
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  size="small"
                  clearable
                  @keydown.enter.native.prevent="searchTable"
                  v-model="searchForm.tenantStatus"
                  placeholder="请选择状态"
                >
                  <el-option :label="'启用'" :value="1"> </el-option>
                  <el-option :label="'禁用'" :value="0"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="开始时间">
                <el-date-picker
                  clearable
                  v-model="searchForm.beginDateTime"
                  size="small"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="结束时间">
                <el-date-picker
                  clearable
                  v-model="searchForm.endDateTime"
                  size="small"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'tenant_add'" size="small" type="primary" @click="handleAdd">添加</el-button>
            <el-button v-permission="'tenant_batchDel'" size="small" @click="batchDelete">批量删除</el-button>
          </div>
          <el-table slot="table" slot-scope="{loading}" v-loading="loading" ref="multipleTable" :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <el-tag :type="scope.row[item.slot]?'success':'danger'">{{scope.row[item.slot]?'启用':'禁用'}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="150"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                    v-permission="'tenant_edit'"
                    style="margin-right:6px"
                    @click="handleEdit(scope.row)"
                    type="text"
                    size="small"
                    >编辑</el-button
                  >
                </template>
                <template>
                  <el-popconfirm
                  style="margin-right:6px"
                  @confirm="changeStatus(scope.row.id)"
                    :title="'您确定要'+(scope.row.tenantStatus == 0 ? '启用' : '禁用')+'该租户吗？'">
                    <el-button v-permission="'tenant_status'" type="text" size="small" slot="reference">{{ scope.row.tenantStatus == 0 ? '启用' : '禁用'}}</el-button>
                  </el-popconfirm>
                </template>
                <template>
                  <el-popconfirm
                  @confirm="handleDelete(scope.row.id)"
                    title="您确定要删除该租户吗？">
                    <el-button v-permission="'tenant_del'" type="text" size="small" slot="reference">删除</el-button>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <div style="width: 100%; padding: 0 10px">
        <el-form
              :model="tenantRuleForm"
              :rules="configRules"
              ref="tenantRuleForm"
              label-width="100px"
              class="demo-ruleForm"
            >
              <el-form-item label="租户名称" prop="tenantName">
                <el-input
                  size="small"
                  placeholder="请输入租户名称"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.tenantName"
                ></el-input>
              </el-form-item>
              <el-form-item label="域名" prop="domainName">
                <el-input
                  size="small"
                  placeholder="请输入域名"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.domainName"
                ></el-input>
              </el-form-item>
              <el-form-item label="联系人" prop="contacts">
                <el-input
                  size="small"
                  placeholder="请输入联系人"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.contacts"
                ></el-input>
              </el-form-item>
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  size="small"
                  placeholder="请输入联系电话"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.contactNumber"
                ></el-input>
              </el-form-item>
              <el-form-item label="联系地址" prop="address">
                <el-input
                  size="small"
                  placeholder="请输入联系地址"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.address"
                ></el-input>
              </el-form-item>
              <el-form-item label="账号限额" prop="accountLimit">
                <el-input
                  size="small"
                  placeholder="请输入账号限额"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.accountLimit"
                ></el-input>
              </el-form-item>
              <el-form-item label="过期时间" prop="expireTime">
                <!-- <el-input v-model="tenantRuleForm.expireTime" placeholder=""></el-input> -->
                <el-date-picker
                  v-model="tenantRuleForm.expireTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择过期时间">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="授权码" prop="licenseKey">
                <el-input
                  size="small"
                  placeholder="请输入授权码"
                  minlength="2"
                  maxlength="32"
                  v-model.trim="tenantRuleForm.licenseKey"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注" prop="comments">
                <el-input
                  size="small"
                  placeholder="请输入备注"
                  maxlength="200"
                  type="textarea"
                  v-model.trim="tenantRuleForm.comments"
                ></el-input>
              </el-form-item>

              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  @click="submitConfigForm('tenantRuleForm')"
                  >保存</el-button
                >
                <el-button size="small" @click="resetForm('tenantRuleForm')"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import _ from 'lodash';
const defaultSearchForm = {
  tenantName: '',
  domainName: '',
  tenantStatus: '',
  beginDateTime: '',
  endDateTime: ''
};
const defaultConfigForm = {
  "accountLimit": 0,
  "address": "",
  "backgroundImage": "",
  "comments": "",
  "contactNumber": "",
  "contacts": "",
  "domainName": "",
  "expireTime": "",
  "id": 0,
  "licenseKey": "",
  "tenantName": "",
  "tenantStatus": 0
};
export default {
  components: {Grid},
  destroyed() {
    this.searchEventBus.$off();
    this.searchConfigEventBus.$off();
  },
  data() {
    this.searchEventBus = new Vue();
    this.searchConfigEventBus = new Vue();
    return {
      configDrawer: false,
      configDrawerName: '添加',
      configStatus: true,
      tenantRuleForm: _.cloneDeep(defaultConfigForm),
      configRules: {
        tenantName: [
          {required: true, message: '请输入租户名称', trigger: 'change,blur'}
        ],
        contacts: [
          {required: true, message: '请输入联系人', trigger: 'change,blur'}
        ],
        domainName: [
          {required: true, message: '请输入域名', trigger: 'change,blur'}
        ],
        address: [
          {required: true, message: '请输入域名', trigger: 'change,blur'}
        ],
        licenseKey: [
          {required: true, message: '请输入授权码', trigger: 'change,blur'}
        ],
      },
      configTableData: [],
      itemId: null,
      configDialogName: '',
      configDialog: false,
      status: true,
      rules: {
        dictName: [
          {required: true, message: '请输入字典名称', trigger: 'change,blur'}
        ],
        dictCode: [
          {required: true, message: '请输入字典代码', trigger: 'change,blur'}
        ],
        dictOrder: [
          {required: true, message: '请输入字典排序', trigger: 'change,blur'},
        ],
      },
      drawerName: '添加',
      drawer: false,
      direction: 'rtl',
      searchForm: _.cloneDeep(defaultSearchForm),
      columns: [
        {
          title: '租户名称',
          key: 'tenantName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '域名',
          key: 'domainName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '联系人',
          key: 'contacts',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '联系电话',
          key: 'contactNumber',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '账号限额',
          key: 'accountLimit',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '过期时间',
          key: 'expireTime',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '授权码',
          key: 'licenseKey',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
        {
          title: '租户状态',
          slot: 'tenantStatus',
          sortType: 'desc',
          tooltip: true,
          minWidth: 170,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
  },

  methods: {
    changeStatus(id) {
      this.$api['tenant/switch-tenantstatus']({id: id}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: '状态已修改',
          type: 'success'
        });
      });
    },
    handleAdd() {
      this.drawer = true;
      this.tenantRuleForm = _.cloneDeep(defaultConfigForm);
      this.drawerName = '添加';
    },
    handleEdit(row) {
      this.tenantRuleForm = Object.assign({}, row);
      this.drawer = true;
      this.drawerName = '编辑';
    },
    batchDelete() {
      let ids = [];
      this.$refs.multipleTable.selection.forEach(e => {
        ids.push(e.id);
      });
      console.info(ids, this.$refs.multipleTable.selection);
      if (!ids.length) {
        this.$message.info('请先选择您要删除的项');
        return;
      }
      this.$confirm('此操作将删除租户信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['tenant/tenant-delete']({ids: ids}).then(data => {
          this.$refs.grid.query();
          this.$message({
            message: '删除成功',
            type: 'success'
          });
        });
      }).catch(err => {});
    },
    handleDelete(e) {
      this.$api['tenant/tenant-delete']({ids: [e]}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      this.searchForm = _.cloneDeep(defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getColumn(e) {
      this.columns = e;
    },
    getDatas(e) {
      this.tableData = e;
    },
    handleConfigEdit(row) {
      this.configStatus = !!row.dictStatus;
      this.tenantRuleForm = Object.assign({}, row);
      this.configDrawer = true;
      this.configDrawerName = '编辑';
    },
    handleConfigDelete(e) {
      this.$api['sysDict/dict-item-delete']({ids: [e]}).then(data => {
        this.$refs.configGrid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    submitConfigForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$api['tenant/tenant-save'](this.tenantRuleForm).then(data => {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            this.drawer = false;
            this.$refs.grid.query();
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
