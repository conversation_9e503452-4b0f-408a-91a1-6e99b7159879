{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\index.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\utils\\index.js", "mtime": 1753781340218}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1745221301271}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.array.find-index\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.string.includes\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.regexp.split\";\nimport _slicedToArray from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport \"core-js/modules/es6.number.constructor\";\nimport \"core-js/modules/es6.number.is-nan\";\nimport uuid from \"./uuid\";\nexport { uuid };\nexport function assert(condition, msg) {\n  if (!condition) {\n    throw new Error(\"[Apior] \".concat(msg));\n  }\n}\nexport function getAmountChinese(val) {\n  var amount = +val;\n  if (Number.isNaN(amount) || amount < 0) {\n    return '';\n  }\n  var NUMBER = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];\n  var N_UNIT1 = ['', '拾', '佰', '仟'];\n  var N_UNIT2 = ['', '万', '亿', '万亿'];\n  var D_UNIT = ['角', '分', '厘', '毫'];\n  var _amount$toString$spli = amount.toString().split('.'),\n    _amount$toString$spli2 = _slicedToArray(_amount$toString$spli, 2),\n    integer = _amount$toString$spli2[0],\n    decimal = _amount$toString$spli2[1];\n  if (integer && integer.length > 12) {\n    return '金额过大无法计算';\n  }\n  var res = '';\n  // 整数部分\n  if (integer) {\n    for (var i = 0, len = integer.length; i < len; i++) {\n      var num = integer.charAt(i);\n      var isZero = num === '0';\n      var pos = len - i - 1; // 排除个位后 所处的索引位置\n      var isMaxUniPos = pos % 4 === 0;\n      var isZeroNext = integer.charAt(i + 1) === '0';\n      if (!(isZero && (isZeroNext || isMaxUniPos))) {\n        // 当前位 等于 0 且下一位也等于 0 则可跳过计算\n        res += NUMBER[num];\n        if (!isZero) {\n          res += N_UNIT1[pos % 4];\n        }\n      }\n      if (isMaxUniPos) {\n        res += N_UNIT2[Math.floor(pos / 4)];\n      }\n    }\n    res += '圆';\n  }\n  // 小数部分\n  if (parseInt(decimal)) {\n    var loopCount = Math.min(decimal.length, 4);\n    for (var _i2 = 0; _i2 < loopCount; _i2++) {\n      var _num = decimal.charAt(_i2);\n      if (_num !== '0') {\n        res += NUMBER[_num] + D_UNIT[_i2];\n      }\n    }\n  } else {\n    res += '整';\n  }\n  return res;\n}\n\n/**\r\n * 计算两个时间差经历的时间的文字描述\r\n * @param {*} timestamp - 毫秒\r\n */\nexport var timeCalculate = function timeCalculate(start, end) {\n  var label = ['分钟', '小时', '天', '月', '年'];\n  var unit = [60, 60, 24, 30, 12];\n  var restTime = Math.floor((end - start) / 1000);\n  var res = '';\n  for (var i = 0, len = unit.length; i < len; i++) {\n    var pos = len - i; // 从年开始算，分钟换算成年 === Math.pow(60, 4)\n    var temp = unit.slice(0, pos).reduce(function (p, c) {\n      return p * c;\n    }, 1);\n    var time = Math.floor(restTime / temp);\n    time > 0 && (res += time + label[pos - 1]);\n    restTime -= time * temp;\n  }\n  return res;\n};\n\n/**\r\n * 简易防抖函数\r\n * @param {Function} func -防抖目标函数\r\n * @param {Number}} gap - 防抖时间间隔\r\n */\nexport var debounce = function debounce(func, gap) {\n  var timer;\n  return function () {\n    var _arguments = arguments,\n      _this = this;\n    timer && clearTimeout(timer);\n    timer = setTimeout(function () {\n      func.apply(_this, _arguments);\n    }, gap);\n  };\n};\n/**\r\n * 下划线转驼峰\r\n * @param {String} name - 字符串\r\n */\nexport var toHump = function toHump(name) {\n  return name.replace(/\\_(\\w)/g, function (all, letter) {\n    return letter.toUpperCase();\n  });\n};\n/**\r\n * 将用户输入的连续单个数字合并为一个数\r\n * @param {Array} expressions - 记录计算表达式的数组\r\n * @returns {Array} 新的数组\r\n */\nexport var mergeNumberOfExps = function mergeNumberOfExps(expressions) {\n  var res = [];\n  var isNumChar = function isNumChar(n) {\n    return /^[\\d|\\.]$/.test(n);\n  };\n  for (var i = 0; i < expressions.length; i++) {\n    if (i > 0 && isNumChar(expressions[i - 1]) && isNumChar(expressions[i])) {\n      res[res.length - 1] += expressions[i];\n      continue;\n    }\n    res.push(expressions[i]);\n  }\n  return res;\n};\n/**\r\n * 校验表达式是否符合计算法则\r\n * @param {Array} expressions - 合并数字后的表达式数组\r\n * @returns {Boolean}\r\n */\nexport var validExp = function validExp(expressions) {\n  var mergeNum = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var temp = mergeNum ? mergeNumberOfExps(expressions) : expressions;\n  var arr = temp.filter(function (t) {\n    return !'()'.includes(t);\n  });\n  // 去括号后 length应该为奇数  并且第一个字符和最后一个字符应该为数字而非计算符号\n  if (temp.length % 2 === 0 || arr.length % 2 === 0 || Number.isNaN(+arr[0]) || Number.isNaN(+arr[arr.length - 1])) {\n    return false;\n  }\n  for (var i = 0; i < arr.length - 1; i += 2) {\n    if (typeof +arr[i] !== 'number' || !Number.isNaN(+arr[i + 1])) {\n      return false;\n    }\n  }\n  return true;\n};\n\n/**\r\n * 中缀转后缀（逆波兰 Reverse Polish Notation）\r\n * @param {Array} exps - 中缀表达式数组\r\n */\nexport var toRPN = function toRPN(exps) {\n  var s1 = []; // 符号栈\n  var s2 = []; // 输出栈\n  var getTopVal = function getTopVal(stack) {\n    return stack.length > 0 ? stack[stack.length - 1] : null;\n  };\n  var levelCompare = function levelCompare(c1, c2) {\n    var getIndex = function getIndex(c) {\n      return ['+-', '×÷', '()'].findIndex(function (t) {\n        return t.includes(c);\n      });\n    };\n    return getIndex(c1) - getIndex(c2);\n  };\n  exps.forEach(function (t) {\n    if (typeof t === 'string' && Number.isNaN(Number(t))) {\n      // 是符号\n      if (t === '(') {\n        s1.push(t);\n      } else if (t === ')') {\n        var popVal;\n        do {\n          popVal = s1.pop();\n          popVal !== '(' && s2.push(popVal);\n        } while (s1.length && popVal !== '(');\n      } else {\n        var topVal = getTopVal(s1);\n        if (!topVal) {\n          // s1 为空 直接push\n          s1.push(t);\n        } else {\n          while (topVal && topVal !== '(' && levelCompare(topVal, t) >= 0) {\n            // 优先级 >= t 弹出到s2\n            s2.push(s1.pop());\n            topVal = getTopVal(s1);\n          }\n          s1.push(t);\n        }\n      }\n      return;\n    }\n    s2.push(t); // 数字直接入栈\n  });\n  while (s1.length) {\n    s2.push(s1.pop());\n  }\n  return s2;\n};\n/**\r\n * 计算后缀表达式的值\r\n * @param {Array} rpnExps - 后缀表达式\r\n */\nexport var calcRPN = function calcRPN(rpnExps) {\n  rpnExps = rpnExps.concat();\n  var calc = function calc(x, y, type) {\n    var a1 = Number(x),\n      a2 = Number(y);\n    switch (type) {\n      case '+':\n        return a1 + a2;\n      case '-':\n        return a1 - a2;\n      case '×':\n        return a1 * a2;\n      case '÷':\n        return a1 / a2;\n    }\n  };\n  for (var i = 2; i < rpnExps.length; i++) {\n    if ('+-×÷'.includes(rpnExps[i])) {\n      var val = calc(rpnExps[i - 2], rpnExps[i - 1], rpnExps[i]);\n      rpnExps.splice(i - 2, 3, val);\n      i = i - 2;\n    }\n  }\n  return rpnExps[0];\n};", {"version": 3, "names": ["uuid", "assert", "condition", "msg", "Error", "concat", "getAmountChinese", "val", "amount", "Number", "isNaN", "NUMBER", "N_UNIT1", "N_UNIT2", "D_UNIT", "_amount$toString$spli", "toString", "split", "_amount$toString$spli2", "_slicedToArray", "integer", "decimal", "length", "res", "i", "len", "num", "char<PERSON>t", "isZero", "pos", "isMaxUniPos", "isZeroNext", "Math", "floor", "parseInt", "loopCount", "min", "timeCalculate", "start", "end", "label", "unit", "restTime", "temp", "slice", "reduce", "p", "c", "time", "debounce", "func", "gap", "timer", "_arguments", "arguments", "_this", "clearTimeout", "setTimeout", "apply", "toHump", "name", "replace", "all", "letter", "toUpperCase", "mergeNumberOfExps", "expressions", "isNumChar", "n", "test", "push", "validExp", "mergeNum", "undefined", "arr", "filter", "t", "includes", "toRPN", "exps", "s1", "s2", "getTopVal", "stack", "levelCompare", "c1", "c2", "getIndex", "findIndex", "for<PERSON>ach", "popVal", "pop", "topVal", "calcRPN", "rpnExps", "calc", "x", "y", "type", "a1", "a2", "splice"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/utils/index.js"], "sourcesContent": ["import uuid from './uuid';\r\n\r\nexport {\r\n  uuid\r\n};\r\n\r\nexport function assert(condition, msg) {\r\n  if (!condition) {\r\n    throw new Error(`[Apior] ${msg}`);\r\n  }\r\n}\r\nexport function getAmountChinese(val) {\r\n  const amount = +val;\r\n  if (Number.isNaN(amount) || amount < 0) {\r\n    return '';\r\n  }\r\n  const NUMBER = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];\r\n  const N_UNIT1 = ['', '拾', '佰', '仟'];\r\n  const N_UNIT2 = ['', '万', '亿', '万亿'];\r\n  const D_UNIT = ['角', '分', '厘', '毫'];\r\n  let [integer, decimal] = amount.toString().split('.');\r\n  if (integer && integer.length > 12) {\r\n    return '金额过大无法计算';\r\n  }\r\n  let res = '';\r\n  // 整数部分\r\n  if (integer) {\r\n    for (let i = 0, len = integer.length; i < len; i++) {\r\n      const num = integer.charAt(i);\r\n      const isZero = num === '0';\r\n      const pos = len - i - 1; // 排除个位后 所处的索引位置\r\n      const isMaxUniPos = pos % 4 === 0;\r\n      const isZeroNext = integer.charAt(i + 1) === '0';\r\n      if (!(isZero && (isZeroNext || isMaxUniPos))) { // 当前位 等于 0 且下一位也等于 0 则可跳过计算\r\n        res += NUMBER[num];\r\n        if (!isZero) {\r\n          res += N_UNIT1[pos % 4];\r\n        }\r\n      }\r\n      if (isMaxUniPos) {\r\n        res += N_UNIT2[Math.floor(pos / 4)];\r\n      }\r\n    }\r\n    res += '圆';\r\n  }\r\n  // 小数部分\r\n  if (parseInt(decimal)) {\r\n    const loopCount = Math.min(decimal.length, 4);\r\n    for (let i = 0; i < loopCount; i++) {\r\n      const num = decimal.charAt(i);\r\n      if (num !== '0') {\r\n        res += NUMBER[num] + D_UNIT[i];\r\n      }\r\n    }\r\n  } else {\r\n    res += '整';\r\n  }\r\n  return res;\r\n}\r\n\r\n/**\r\n * 计算两个时间差经历的时间的文字描述\r\n * @param {*} timestamp - 毫秒\r\n */\r\nexport const timeCalculate = (start, end) => {\r\n  const label = ['分钟', '小时', '天', '月', '年'];\r\n  const unit = [60, 60, 24, 30, 12];\r\n  let restTime = Math.floor((end - start) / 1000);\r\n  let res = '';\r\n  for (let i = 0, len = unit.length; i < len; i++) {\r\n    const pos = len - i; // 从年开始算，分钟换算成年 === Math.pow(60, 4)\r\n    const temp = unit.slice(0, pos).reduce((p, c) => p * c, 1);\r\n    const time = Math.floor(restTime / temp);\r\n    time > 0 && (res += time + label[pos - 1]);\r\n    restTime -= time * temp;\r\n  }\r\n  return res;\r\n};\r\n\r\n\r\n/**\r\n * 简易防抖函数\r\n * @param {Function} func -防抖目标函数\r\n * @param {Number}} gap - 防抖时间间隔\r\n */\r\nexport const debounce = (func, gap) => {\r\n  let timer;\r\n  return function () {\r\n    timer && clearTimeout(timer);\r\n    timer = setTimeout(() => {\r\n      func.apply(this, arguments);\r\n    }, gap);\r\n  };\r\n};\r\n/**\r\n * 下划线转驼峰\r\n * @param {String} name - 字符串\r\n */\r\nexport const toHump = name => name.replace(/\\_(\\w)/g, function (all, letter) {\r\n  return letter.toUpperCase();\r\n});\r\n/**\r\n * 将用户输入的连续单个数字合并为一个数\r\n * @param {Array} expressions - 记录计算表达式的数组\r\n * @returns {Array} 新的数组\r\n */\r\nexport const mergeNumberOfExps = expressions => {\r\n  const res = [];\r\n  const isNumChar = n => /^[\\d|\\.]$/.test(n);\r\n  for (let i = 0; i < expressions.length; i++) {\r\n    if (i > 0 && isNumChar(expressions[i - 1]) && isNumChar(expressions[i])) {\r\n      res[res.length - 1] += expressions[i];\r\n      continue;\r\n    }\r\n    res.push(expressions[i]);\r\n  }\r\n  return res;\r\n};\r\n/**\r\n * 校验表达式是否符合计算法则\r\n * @param {Array} expressions - 合并数字后的表达式数组\r\n * @returns {Boolean}\r\n */\r\nexport const validExp = (expressions, mergeNum = true) => {\r\n  const temp = mergeNum ? mergeNumberOfExps(expressions) : expressions;\r\n  const arr = temp.filter(t => !'()'.includes(t));\r\n  // 去括号后 length应该为奇数  并且第一个字符和最后一个字符应该为数字而非计算符号\r\n  if (temp.length % 2 === 0 || arr.length % 2 === 0 || Number.isNaN(+arr[0]) || Number.isNaN(+arr[arr.length - 1])) {\r\n    return false;\r\n  }\r\n  for (let i = 0; i < arr.length - 1; i += 2) {\r\n    if (typeof (+arr[i]) !== 'number' || !Number.isNaN(+arr[i + 1])) {\r\n      return false;\r\n    }\r\n  }\r\n  return true;\r\n};\r\n\r\n/**\r\n * 中缀转后缀（逆波兰 Reverse Polish Notation）\r\n * @param {Array} exps - 中缀表达式数组\r\n */\r\nexport const toRPN = exps => {\r\n  const s1 = []; // 符号栈\r\n  const s2 = []; // 输出栈\r\n  const getTopVal = stack => (stack.length > 0 ? stack[stack.length - 1] : null);\r\n  const levelCompare = (c1, c2) => {\r\n    const getIndex = c => ['+-', '×÷', '()'].findIndex(t => t.includes(c));\r\n    return getIndex(c1) - getIndex(c2);\r\n  };\r\n  exps.forEach(t => {\r\n    if (typeof t === 'string' && Number.isNaN(Number(t))) { // 是符号\r\n      if (t === '(') {\r\n        s1.push(t);\r\n      } else if (t === ')') {\r\n        let popVal;\r\n        do {\r\n          popVal = s1.pop();\r\n          popVal !== '(' && s2.push(popVal);\r\n        } while (s1.length && popVal !== '(');\r\n      } else {\r\n        let topVal = getTopVal(s1);\r\n        if (!topVal) { // s1 为空 直接push\r\n          s1.push(t);\r\n        } else {\r\n          while (topVal && topVal !== '(' && levelCompare(topVal, t) >= 0) { // 优先级 >= t 弹出到s2\r\n            s2.push(s1.pop());\r\n            topVal = getTopVal(s1);\r\n          }\r\n          s1.push(t);\r\n        }\r\n      }\r\n      return;\r\n    }\r\n    s2.push(t); // 数字直接入栈\r\n  });\r\n  while (s1.length) {\r\n    s2.push(s1.pop());\r\n  }\r\n  return s2;\r\n};\r\n/**\r\n * 计算后缀表达式的值\r\n * @param {Array} rpnExps - 后缀表达式\r\n */\r\nexport const calcRPN = rpnExps => {\r\n  rpnExps = rpnExps.concat();\r\n  const calc = (x, y, type) => {\r\n    let a1 = Number(x), a2 = Number(y);\r\n    switch (type) {\r\n      case '+': return a1 + a2;\r\n      case '-': return a1 - a2;\r\n      case '×': return a1 * a2;\r\n      case '÷': return a1 / a2;\r\n    }\r\n  };\r\n  for (let i = 2; i < rpnExps.length; i++) {\r\n    if ('+-×÷'.includes(rpnExps[i])) {\r\n      let val = calc(rpnExps[i - 2], rpnExps[i - 1], rpnExps[i]);\r\n      rpnExps.splice(i - 2, 3, val);\r\n      i = i - 2;\r\n    }\r\n  }\r\n  return rpnExps[0];\r\n};\r\n"], "mappings": ";;;;;;;;;;AAAA,OAAOA,IAAI;AAEX,SACEA,IAAI;AAGN,OAAO,SAASC,MAAMA,CAACC,SAAS,EAAEC,GAAG,EAAE;EACrC,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,YAAAC,MAAA,CAAYF,GAAG,CAAE,CAAC;EACnC;AACF;AACA,OAAO,SAASG,gBAAgBA,CAACC,GAAG,EAAE;EACpC,IAAMC,MAAM,GAAG,CAACD,GAAG;EACnB,IAAIE,MAAM,CAACC,KAAK,CAACF,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;IACtC,OAAO,EAAE;EACX;EACA,IAAMG,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjE,IAAMC,OAAO,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,IAAMC,OAAO,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EACpC,IAAMC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,IAAAC,qBAAA,GAAyBP,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAAAC,sBAAA,GAAAC,cAAA,CAAAJ,qBAAA;IAAhDK,OAAO,GAAAF,sBAAA;IAAEG,OAAO,GAAAH,sBAAA;EACrB,IAAIE,OAAO,IAAIA,OAAO,CAACE,MAAM,GAAG,EAAE,EAAE;IAClC,OAAO,UAAU;EACnB;EACA,IAAIC,GAAG,GAAG,EAAE;EACZ;EACA,IAAIH,OAAO,EAAE;IACX,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,OAAO,CAACE,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,IAAME,GAAG,GAAGN,OAAO,CAACO,MAAM,CAACH,CAAC,CAAC;MAC7B,IAAMI,MAAM,GAAGF,GAAG,KAAK,GAAG;MAC1B,IAAMG,GAAG,GAAGJ,GAAG,GAAGD,CAAC,GAAG,CAAC,CAAC,CAAC;MACzB,IAAMM,WAAW,GAAGD,GAAG,GAAG,CAAC,KAAK,CAAC;MACjC,IAAME,UAAU,GAAGX,OAAO,CAACO,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;MAChD,IAAI,EAAEI,MAAM,KAAKG,UAAU,IAAID,WAAW,CAAC,CAAC,EAAE;QAAE;QAC9CP,GAAG,IAAIZ,MAAM,CAACe,GAAG,CAAC;QAClB,IAAI,CAACE,MAAM,EAAE;UACXL,GAAG,IAAIX,OAAO,CAACiB,GAAG,GAAG,CAAC,CAAC;QACzB;MACF;MACA,IAAIC,WAAW,EAAE;QACfP,GAAG,IAAIV,OAAO,CAACmB,IAAI,CAACC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,CAAC;MACrC;IACF;IACAN,GAAG,IAAI,GAAG;EACZ;EACA;EACA,IAAIW,QAAQ,CAACb,OAAO,CAAC,EAAE;IACrB,IAAMc,SAAS,GAAGH,IAAI,CAACI,GAAG,CAACf,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAC7C,KAAK,IAAIE,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGW,SAAS,EAAEX,GAAC,EAAE,EAAE;MAClC,IAAME,IAAG,GAAGL,OAAO,CAACM,MAAM,CAACH,GAAC,CAAC;MAC7B,IAAIE,IAAG,KAAK,GAAG,EAAE;QACfH,GAAG,IAAIZ,MAAM,CAACe,IAAG,CAAC,GAAGZ,MAAM,CAACU,GAAC,CAAC;MAChC;IACF;EACF,CAAC,MAAM;IACLD,GAAG,IAAI,GAAG;EACZ;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAEC,GAAG,EAAK;EAC3C,IAAMC,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzC,IAAMC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACjC,IAAIC,QAAQ,GAAGV,IAAI,CAACC,KAAK,CAAC,CAACM,GAAG,GAAGD,KAAK,IAAI,IAAI,CAAC;EAC/C,IAAIf,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGgB,IAAI,CAACnB,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC/C,IAAMK,GAAG,GAAGJ,GAAG,GAAGD,CAAC,CAAC,CAAC;IACrB,IAAMmB,IAAI,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,EAAEf,GAAG,CAAC,CAACgB,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC;MAAA,OAAKD,CAAC,GAAGC,CAAC;IAAA,GAAE,CAAC,CAAC;IAC1D,IAAMC,IAAI,GAAGhB,IAAI,CAACC,KAAK,CAACS,QAAQ,GAAGC,IAAI,CAAC;IACxCK,IAAI,GAAG,CAAC,KAAKzB,GAAG,IAAIyB,IAAI,GAAGR,KAAK,CAACX,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1Ca,QAAQ,IAAIM,IAAI,GAAGL,IAAI;EACzB;EACA,OAAOpB,GAAG;AACZ,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAM0B,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,IAAI,EAAEC,GAAG,EAAK;EACrC,IAAIC,KAAK;EACT,OAAO,YAAY;IAAA,IAAAC,UAAA,GAAAC,SAAA;MAAAC,KAAA;IACjBH,KAAK,IAAII,YAAY,CAACJ,KAAK,CAAC;IAC5BA,KAAK,GAAGK,UAAU,CAAC,YAAM;MACvBP,IAAI,CAACQ,KAAK,CAACH,KAAI,EAAED,UAAS,CAAC;IAC7B,CAAC,EAAEH,GAAG,CAAC;EACT,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAMQ,MAAM,GAAG,SAATA,MAAMA,CAAGC,IAAI;EAAA,OAAIA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,UAAUC,GAAG,EAAEC,MAAM,EAAE;IAC3E,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7B,CAAC,CAAC;AAAA;AACF;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAGC,WAAW,EAAI;EAC9C,IAAM3C,GAAG,GAAG,EAAE;EACd,IAAM4C,SAAS,GAAG,SAAZA,SAASA,CAAGC,CAAC;IAAA,OAAI,WAAW,CAACC,IAAI,CAACD,CAAC,CAAC;EAAA;EAC1C,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,WAAW,CAAC5C,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC3C,IAAIA,CAAC,GAAG,CAAC,IAAI2C,SAAS,CAACD,WAAW,CAAC1C,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI2C,SAAS,CAACD,WAAW,CAAC1C,CAAC,CAAC,CAAC,EAAE;MACvED,GAAG,CAACA,GAAG,CAACD,MAAM,GAAG,CAAC,CAAC,IAAI4C,WAAW,CAAC1C,CAAC,CAAC;MACrC;IACF;IACAD,GAAG,CAAC+C,IAAI,CAACJ,WAAW,CAAC1C,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOD,GAAG;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMgD,QAAQ,GAAG,SAAXA,QAAQA,CAAIL,WAAW,EAAsB;EAAA,IAApBM,QAAQ,GAAAlB,SAAA,CAAAhC,MAAA,QAAAgC,SAAA,QAAAmB,SAAA,GAAAnB,SAAA,MAAG,IAAI;EACnD,IAAMX,IAAI,GAAG6B,QAAQ,GAAGP,iBAAiB,CAACC,WAAW,CAAC,GAAGA,WAAW;EACpE,IAAMQ,GAAG,GAAG/B,IAAI,CAACgC,MAAM,CAAC,UAAAC,CAAC;IAAA,OAAI,CAAC,IAAI,CAACC,QAAQ,CAACD,CAAC,CAAC;EAAA,EAAC;EAC/C;EACA,IAAIjC,IAAI,CAACrB,MAAM,GAAG,CAAC,KAAK,CAAC,IAAIoD,GAAG,CAACpD,MAAM,GAAG,CAAC,KAAK,CAAC,IAAIb,MAAM,CAACC,KAAK,CAAC,CAACgE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjE,MAAM,CAACC,KAAK,CAAC,CAACgE,GAAG,CAACA,GAAG,CAACpD,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;IAChH,OAAO,KAAK;EACd;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,GAAG,CAACpD,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;IAC1C,IAAI,OAAQ,CAACkD,GAAG,CAAClD,CAAC,CAAE,KAAK,QAAQ,IAAI,CAACf,MAAM,CAACC,KAAK,CAAC,CAACgE,GAAG,CAAClD,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAMsD,KAAK,GAAG,SAARA,KAAKA,CAAGC,IAAI,EAAI;EAC3B,IAAMC,EAAE,GAAG,EAAE,CAAC,CAAC;EACf,IAAMC,EAAE,GAAG,EAAE,CAAC,CAAC;EACf,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAGC,KAAK;IAAA,OAAKA,KAAK,CAAC7D,MAAM,GAAG,CAAC,GAAG6D,KAAK,CAACA,KAAK,CAAC7D,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAAA,CAAC;EAC9E,IAAM8D,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE,EAAEC,EAAE,EAAK;IAC/B,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGxC,CAAC;MAAA,OAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACyC,SAAS,CAAC,UAAAZ,CAAC;QAAA,OAAIA,CAAC,CAACC,QAAQ,CAAC9B,CAAC,CAAC;MAAA,EAAC;IAAA;IACtE,OAAOwC,QAAQ,CAACF,EAAE,CAAC,GAAGE,QAAQ,CAACD,EAAE,CAAC;EACpC,CAAC;EACDP,IAAI,CAACU,OAAO,CAAC,UAAAb,CAAC,EAAI;IAChB,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAInE,MAAM,CAACC,KAAK,CAACD,MAAM,CAACmE,CAAC,CAAC,CAAC,EAAE;MAAE;MACtD,IAAIA,CAAC,KAAK,GAAG,EAAE;QACbI,EAAE,CAACV,IAAI,CAACM,CAAC,CAAC;MACZ,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;QACpB,IAAIc,MAAM;QACV,GAAG;UACDA,MAAM,GAAGV,EAAE,CAACW,GAAG,CAAC,CAAC;UACjBD,MAAM,KAAK,GAAG,IAAIT,EAAE,CAACX,IAAI,CAACoB,MAAM,CAAC;QACnC,CAAC,QAAQV,EAAE,CAAC1D,MAAM,IAAIoE,MAAM,KAAK,GAAG;MACtC,CAAC,MAAM;QACL,IAAIE,MAAM,GAAGV,SAAS,CAACF,EAAE,CAAC;QAC1B,IAAI,CAACY,MAAM,EAAE;UAAE;UACbZ,EAAE,CAACV,IAAI,CAACM,CAAC,CAAC;QACZ,CAAC,MAAM;UACL,OAAOgB,MAAM,IAAIA,MAAM,KAAK,GAAG,IAAIR,YAAY,CAACQ,MAAM,EAAEhB,CAAC,CAAC,IAAI,CAAC,EAAE;YAAE;YACjEK,EAAE,CAACX,IAAI,CAACU,EAAE,CAACW,GAAG,CAAC,CAAC,CAAC;YACjBC,MAAM,GAAGV,SAAS,CAACF,EAAE,CAAC;UACxB;UACAA,EAAE,CAACV,IAAI,CAACM,CAAC,CAAC;QACZ;MACF;MACA;IACF;IACAK,EAAE,CAACX,IAAI,CAACM,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC;EACF,OAAOI,EAAE,CAAC1D,MAAM,EAAE;IAChB2D,EAAE,CAACX,IAAI,CAACU,EAAE,CAACW,GAAG,CAAC,CAAC,CAAC;EACnB;EACA,OAAOV,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAMY,OAAO,GAAG,SAAVA,OAAOA,CAAGC,OAAO,EAAI;EAChCA,OAAO,GAAGA,OAAO,CAACzF,MAAM,CAAC,CAAC;EAC1B,IAAM0F,IAAI,GAAG,SAAPA,IAAIA,CAAIC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAK;IAC3B,IAAIC,EAAE,GAAG1F,MAAM,CAACuF,CAAC,CAAC;MAAEI,EAAE,GAAG3F,MAAM,CAACwF,CAAC,CAAC;IAClC,QAAQC,IAAI;MACV,KAAK,GAAG;QAAE,OAAOC,EAAE,GAAGC,EAAE;MACxB,KAAK,GAAG;QAAE,OAAOD,EAAE,GAAGC,EAAE;MACxB,KAAK,GAAG;QAAE,OAAOD,EAAE,GAAGC,EAAE;MACxB,KAAK,GAAG;QAAE,OAAOD,EAAE,GAAGC,EAAE;IAC1B;EACF,CAAC;EACD,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,OAAO,CAACxE,MAAM,EAAEE,CAAC,EAAE,EAAE;IACvC,IAAI,MAAM,CAACqD,QAAQ,CAACiB,OAAO,CAACtE,CAAC,CAAC,CAAC,EAAE;MAC/B,IAAIjB,GAAG,GAAGwF,IAAI,CAACD,OAAO,CAACtE,CAAC,GAAG,CAAC,CAAC,EAAEsE,OAAO,CAACtE,CAAC,GAAG,CAAC,CAAC,EAAEsE,OAAO,CAACtE,CAAC,CAAC,CAAC;MAC1DsE,OAAO,CAACO,MAAM,CAAC7E,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEjB,GAAG,CAAC;MAC7BiB,CAAC,GAAGA,CAAC,GAAG,CAAC;IACX;EACF;EACA,OAAOsE,OAAO,CAAC,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}]}