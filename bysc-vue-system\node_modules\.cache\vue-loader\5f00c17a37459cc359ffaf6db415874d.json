{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue", "mtime": 1753781119908}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\nimport Vue from \"vue\";\r\nimport Grid from \"@/components/Grid\";\r\nimport TenantForm from \"@/bysc_system/components/TenantForm.vue\";\r\nimport _ from \"lodash\";\r\nconst defaultSearchForm = {\r\n  tenantCode: \"\",\r\n  tenantName: \"\",\r\n  tenantAdmin: \"\",\r\n  isSelected: \"\",\r\n};\r\nconst defaultConfigForm = {\r\n  id: 0,\r\n  tenantCode: \"\",\r\n  tenantName: \"\",\r\n  tenantAdmin: \"\",\r\n  comments: \"\",\r\n  isSelected: false,\r\n};\r\nexport default {\r\n  components: {Grid, TenantForm},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n    this.searchConfigEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    this.searchConfigEventBus = new Vue();\r\n    return {\r\n      configDrawer: false,\r\n      configDrawerName: \"添加\",\r\n      configStatus: true,\r\n      tenantRuleForm: _.cloneDeep(defaultConfigForm),\r\n      isEditMode: false,\r\n      submitLoading: false,\r\n      configTableData: [],\r\n      itemId: null,\r\n      configDialogName: \"\",\r\n      configDialog: false,\r\n      status: true,\r\n      rules: {\r\n        dictName: [\r\n          {required: true, message: \"请输入字典名称\", trigger: \"change,blur\"},\r\n        ],\r\n        dictCode: [\r\n          {required: true, message: \"请输入字典代码\", trigger: \"change,blur\"},\r\n        ],\r\n        dictOrder: [\r\n          {required: true, message: \"请输入字典排序\", trigger: \"change,blur\"},\r\n        ],\r\n      },\r\n      drawerName: \"添加\",\r\n      drawer: false,\r\n      direction: \"rtl\",\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: \"租户编码\",\r\n          key: \"tenantCode\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"租户名称\",\r\n          key: \"tenantName\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"租户管理员\",\r\n          key: \"tenantAdmin\",\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: \"备注\",\r\n          key: \"comments\",\r\n          tooltip: true,\r\n          minWidth: 200,\r\n        },\r\n        {\r\n          title: \"是否选中\",\r\n          slot: \"isSelected\",\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n      ],\r\n      tableData: [],\r\n    };\r\n  },\r\n  mounted() {},\r\n\r\n  methods: {\r\n    handleSelectionChange(row) {\r\n      // 处理选中状态变化\r\n      console.log(\"选中状态变化:\", row.tenantName, row.isSelected);\r\n      // 这里可以添加API调用来保存选中状态\r\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\r\n    },\r\n\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      this.tenantRuleForm = _.cloneDeep(defaultConfigForm);\r\n      this.drawerName = \"新建租户\";\r\n      this.isEditMode = false;\r\n    },\r\n    handleEdit(row) {\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.drawer = true;\r\n      this.drawerName = \"编辑租户\";\r\n      this.isEditMode = true;\r\n    },\r\n    batchDelete() {\r\n      let ids = [];\r\n      this.$refs.multipleTable.selection.forEach(e => {\r\n        ids.push(e.id);\r\n      });\r\n      console.info(ids, this.$refs.multipleTable.selection);\r\n      if (!ids.length) {\r\n        this.$message.info(\"请先选择您要删除的项\");\r\n        return;\r\n      }\r\n      this.$confirm(\"此操作将删除租户信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$api[\"tenant/tenant-delete\"]({ids: ids}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n        })\r\n        .catch(err => {});\r\n    },\r\n    handleDelete(e) {\r\n      this.$api[\"tenant/tenant-delete\"]({ids: [e]}).then(data => {\r\n        this.$refs.grid.query();\r\n        this.$message({\r\n          message: \"删除成功\",\r\n          type: \"success\",\r\n        });\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n    handleConfigEdit(row) {\r\n      this.configStatus = !!row.dictStatus;\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.configDrawer = true;\r\n      this.configDrawerName = \"编辑\";\r\n    },\r\n    handleConfigDelete(e) {\r\n      this.$api[\"sysDict/dict-item-delete\"]({ids: [e]}).then(data => {\r\n        this.$refs.configGrid.query();\r\n        this.$message({\r\n          message: \"删除成功\",\r\n          type: \"success\",\r\n        });\r\n      });\r\n    },\r\n    // 表单提交处理\r\n    handleFormSubmit(formData) {\r\n      this.submitLoading = true;\r\n      this.$api[\"tenant/tenant-save\"](formData)\r\n        .then(data => {\r\n          this.$message({\r\n            message: \"保存成功\",\r\n            type: \"success\",\r\n          });\r\n          this.drawer = false;\r\n          this.$refs.grid.query();\r\n          this.resetFormData();\r\n        })\r\n        .catch(error => {\r\n          console.error(\"保存失败:\", error);\r\n        })\r\n        .finally(() => {\r\n          this.submitLoading = false;\r\n        });\r\n    },\r\n\r\n    // 表单取消处理\r\n    handleFormCancel() {\r\n      this.drawer = false;\r\n      this.resetFormData();\r\n    },\r\n\r\n    // 重置表单数据\r\n    resetFormData() {\r\n      this.tenantRuleForm = _.cloneDeep(defaultConfigForm);\r\n      this.isEditMode = false;\r\n      this.submitLoading = false;\r\n    },\r\n  },\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA8NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/tenant", "sourcesContent": ["<!--\r\n * @Author: czw\r\n * @Date: 2022-11-03 17:55:45\r\n * @LastEditors: czw\r\n * @LastEditTime: 2022-11-04 10:11:20\r\n * @FilePath: \\kdsp_vue_clear\\src\\systemView\\views\\menus\\index.vue\r\n * @Description:\r\n *\r\n * Copyright (c) 2022 by czw/bysc, All Rights Reserved.\r\n-->\r\n<!--  -->\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"tenant/tenant-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"租户编码\">\r\n                <el-input\r\n                  style=\"width: 200px; margin: 0 10px 0 0\"\r\n                  v-model.trim=\"searchForm.tenantCode\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户编码\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"租户名称\">\r\n                <el-input\r\n                  style=\"width: 200px; margin: 0 10px 0 0\"\r\n                  v-model.trim=\"searchForm.tenantName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"租户管理员\">\r\n                <el-input\r\n                  style=\"width: 200px; margin: 0 10px 0 0\"\r\n                  v-model.trim=\"searchForm.tenantAdmin\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户管理员\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"是否选中\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.isSelected\"\r\n                  placeholder=\"请选择是否选中\"\r\n                >\r\n                  <el-option :label=\"'是'\" :value=\"true\"> </el-option>\r\n                  <el-option :label=\"'否'\" :value=\"false\"> </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button\r\n                >\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button\r\n              v-permission=\"'tenant_add'\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n              >新建租户</el-button\r\n            >\r\n            <el-button\r\n              v-permission=\"'tenant_batchDel'\"\r\n              size=\"small\"\r\n              @click=\"batchDelete\"\r\n              >删除租户</el-button\r\n            >\r\n          </div>\r\n          <el-table\r\n            slot=\"table\"\r\n            slot-scope=\"{ loading }\"\r\n            v-loading=\"loading\"\r\n            ref=\"multipleTable\"\r\n            :data=\"tableData\"\r\n            stripe\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              type=\"selection\"\r\n              width=\"55\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              label=\"序号\"\r\n              type=\"index\"\r\n              width=\"50\"\r\n            >\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'isSelected'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-switch\r\n                    v-model=\"scope.row.isSelected\"\r\n                    @change=\"handleSelectionChange(scope.row)\"\r\n                    active-text=\"是\"\r\n                    inactive-text=\"否\"\r\n                  >\r\n                  </el-switch>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row[item.slot] ? 'success' : 'danger'\">{{\r\n                    scope.row[item.slot] ? \"启用\" : \"禁用\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"120\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <template>\r\n                  <el-button\r\n                    v-permission=\"'tenant_edit'\"\r\n                    style=\"margin-right: 6px\"\r\n                    @click=\"handleEdit(scope.row)\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    >编辑</el-button\r\n                  >\r\n                </template>\r\n\r\n                <template>\r\n                  <el-popconfirm\r\n                    @confirm=\"handleDelete(scope.row.id)\"\r\n                    title=\"您确定要删除该租户吗？\"\r\n                  >\r\n                    <el-button\r\n                      v-permission=\"'tenant_del'\"\r\n                      type=\"text\"\r\n                      size=\"small\"\r\n                      slot=\"reference\"\r\n                      >删除</el-button\r\n                    >\r\n                  </el-popconfirm>\r\n                </template>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      :title=\"drawerName\"\r\n      :visible.sync=\"drawer\"\r\n      :direction=\"direction\"\r\n      :wrapperClosable=\"false\"\r\n    >\r\n      <TenantForm\r\n        v-model=\"tenantRuleForm\"\r\n        :is-edit=\"isEditMode\"\r\n        :loading=\"submitLoading\"\r\n        @submit=\"handleFormSubmit\"\r\n        @cancel=\"handleFormCancel\"\r\n        ref=\"tenantForm\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from \"vue\";\r\nimport Grid from \"@/components/Grid\";\r\nimport TenantForm from \"@/bysc_system/components/TenantForm.vue\";\r\nimport _ from \"lodash\";\r\nconst defaultSearchForm = {\r\n  tenantCode: \"\",\r\n  tenantName: \"\",\r\n  tenantAdmin: \"\",\r\n  isSelected: \"\",\r\n};\r\nconst defaultConfigForm = {\r\n  id: 0,\r\n  tenantCode: \"\",\r\n  tenantName: \"\",\r\n  tenantAdmin: \"\",\r\n  comments: \"\",\r\n  isSelected: false,\r\n};\r\nexport default {\r\n  components: {Grid, TenantForm},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n    this.searchConfigEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    this.searchConfigEventBus = new Vue();\r\n    return {\r\n      configDrawer: false,\r\n      configDrawerName: \"添加\",\r\n      configStatus: true,\r\n      tenantRuleForm: _.cloneDeep(defaultConfigForm),\r\n      isEditMode: false,\r\n      submitLoading: false,\r\n      configTableData: [],\r\n      itemId: null,\r\n      configDialogName: \"\",\r\n      configDialog: false,\r\n      status: true,\r\n      rules: {\r\n        dictName: [\r\n          {required: true, message: \"请输入字典名称\", trigger: \"change,blur\"},\r\n        ],\r\n        dictCode: [\r\n          {required: true, message: \"请输入字典代码\", trigger: \"change,blur\"},\r\n        ],\r\n        dictOrder: [\r\n          {required: true, message: \"请输入字典排序\", trigger: \"change,blur\"},\r\n        ],\r\n      },\r\n      drawerName: \"添加\",\r\n      drawer: false,\r\n      direction: \"rtl\",\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: \"租户编码\",\r\n          key: \"tenantCode\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"租户名称\",\r\n          key: \"tenantName\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"租户管理员\",\r\n          key: \"tenantAdmin\",\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: \"备注\",\r\n          key: \"comments\",\r\n          tooltip: true,\r\n          minWidth: 200,\r\n        },\r\n        {\r\n          title: \"是否选中\",\r\n          slot: \"isSelected\",\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n      ],\r\n      tableData: [],\r\n    };\r\n  },\r\n  mounted() {},\r\n\r\n  methods: {\r\n    handleSelectionChange(row) {\r\n      // 处理选中状态变化\r\n      console.log(\"选中状态变化:\", row.tenantName, row.isSelected);\r\n      // 这里可以添加API调用来保存选中状态\r\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\r\n    },\r\n\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      this.tenantRuleForm = _.cloneDeep(defaultConfigForm);\r\n      this.drawerName = \"新建租户\";\r\n      this.isEditMode = false;\r\n    },\r\n    handleEdit(row) {\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.drawer = true;\r\n      this.drawerName = \"编辑租户\";\r\n      this.isEditMode = true;\r\n    },\r\n    batchDelete() {\r\n      let ids = [];\r\n      this.$refs.multipleTable.selection.forEach(e => {\r\n        ids.push(e.id);\r\n      });\r\n      console.info(ids, this.$refs.multipleTable.selection);\r\n      if (!ids.length) {\r\n        this.$message.info(\"请先选择您要删除的项\");\r\n        return;\r\n      }\r\n      this.$confirm(\"此操作将删除租户信息?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$api[\"tenant/tenant-delete\"]({ids: ids}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n        })\r\n        .catch(err => {});\r\n    },\r\n    handleDelete(e) {\r\n      this.$api[\"tenant/tenant-delete\"]({ids: [e]}).then(data => {\r\n        this.$refs.grid.query();\r\n        this.$message({\r\n          message: \"删除成功\",\r\n          type: \"success\",\r\n        });\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n    handleConfigEdit(row) {\r\n      this.configStatus = !!row.dictStatus;\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.configDrawer = true;\r\n      this.configDrawerName = \"编辑\";\r\n    },\r\n    handleConfigDelete(e) {\r\n      this.$api[\"sysDict/dict-item-delete\"]({ids: [e]}).then(data => {\r\n        this.$refs.configGrid.query();\r\n        this.$message({\r\n          message: \"删除成功\",\r\n          type: \"success\",\r\n        });\r\n      });\r\n    },\r\n    // 表单提交处理\r\n    handleFormSubmit(formData) {\r\n      this.submitLoading = true;\r\n      this.$api[\"tenant/tenant-save\"](formData)\r\n        .then(data => {\r\n          this.$message({\r\n            message: \"保存成功\",\r\n            type: \"success\",\r\n          });\r\n          this.drawer = false;\r\n          this.$refs.grid.query();\r\n          this.resetFormData();\r\n        })\r\n        .catch(error => {\r\n          console.error(\"保存失败:\", error);\r\n        })\r\n        .finally(() => {\r\n          this.submitLoading = false;\r\n        });\r\n    },\r\n\r\n    // 表单取消处理\r\n    handleFormCancel() {\r\n      this.drawer = false;\r\n      this.resetFormData();\r\n    },\r\n\r\n    // 重置表单数据\r\n    resetFormData() {\r\n      this.tenantRuleForm = _.cloneDeep(defaultConfigForm);\r\n      this.isEditMode = false;\r\n      this.submitLoading = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"]}]}