{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\components\\TenantForm.vue", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\components\\TenantForm.vue", "mtime": 1753781207105}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\eslint-loader\\index.js", "mtime": 1745221301271}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./TenantForm.vue?vue&type=template&id=d8709494&scoped=true\"\nimport script from \"./TenantForm.vue?vue&type=script&lang=js\"\nexport * from \"./TenantForm.vue?vue&type=script&lang=js\"\nimport style0 from \"./TenantForm.vue?vue&type=style&index=0&id=d8709494&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d8709494\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\bw\\\\idcardbox-vue\\\\bysc-vue-system\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('d8709494')) {\n      api.createRecord('d8709494', component.options)\n    } else {\n      api.reload('d8709494', component.options)\n    }\n    module.hot.accept(\"./TenantForm.vue?vue&type=template&id=d8709494&scoped=true\", function () {\n      api.rerender('d8709494', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/bysc_system/components/TenantForm.vue\"\nexport default component.exports"]}