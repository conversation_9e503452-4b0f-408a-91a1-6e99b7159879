{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\components\\TenantForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\components\\TenantForm.vue", "mtime": 1753781207105}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"core-js/modules/es6.object.keys\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: function _default() {\n        return {\n          id: null,\n          tenantCode: '',\n          tenantName: '',\n          tenantAdmin: '',\n          comments: '',\n          isSelected: false\n        };\n      }\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [{\n          required: true,\n          message: '请输入租户编码',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 32,\n          message: '长度在 2 到 32 个字符',\n          trigger: 'blur'\n        }],\n        tenantName: [{\n          required: true,\n          message: '请输入租户名称',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 50,\n          message: '长度在 2 到 50 个字符',\n          trigger: 'blur'\n        }],\n        tenantAdmin: [{\n          min: 0,\n          max: 50,\n          message: '长度不能超过 50 个字符',\n          trigger: 'blur'\n        }],\n        comments: [{\n          max: 200,\n          message: '长度不能超过 200 个字符',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  watch: {\n    value: {\n      handler: function handler(newVal) {\n        this.formData = _objectSpread({}, newVal);\n      },\n      immediate: true,\n      deep: true\n    },\n    formData: {\n      handler: function handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 提交表单\n    handleSubmit: function handleSubmit() {\n      var _this = this;\n      this.$refs.tenantForm.validate(function (valid) {\n        if (valid) {\n          _this.$emit('submit', _this.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n    // 取消操作\n    handleCancel: function handleCancel() {\n      this.$emit('cancel');\n    },\n    // 重置表单\n    resetForm: function resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n    // 验证表单\n    validate: function validate() {\n      var _this2 = this;\n      return new Promise(function (resolve) {\n        _this2.$refs.tenantForm.validate(function (valid) {\n          resolve(valid);\n        });\n      });\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "value", "type", "Object", "default", "id", "tenantCode", "tenantName", "tenantAdmin", "comments", "isSelected", "isEdit", "Boolean", "loading", "data", "formData", "rules", "required", "message", "trigger", "min", "max", "watch", "handler", "newVal", "_objectSpread", "immediate", "deep", "$emit", "methods", "handleSubmit", "_this", "$refs", "tenantForm", "validate", "valid", "handleCancel", "resetForm", "resetFields", "_this2", "Promise", "resolve"], "sources": ["src/bysc_system/views/tenant/components/TenantForm.vue"], "sourcesContent": ["<!--\n * @Author: czw\n * @Date: 2024-01-01 00:00:00\n * @LastEditors: czw\n * @LastEditTime: 2024-01-01 00:00:00\n * @Description: 租户表单组件\n *\n * Copyright (c) 2024 by czw/bysc, All Rights Reserved.\n-->\n<template>\n  <div class=\"tenant-form\">\n    <el-form\n      :model=\"formData\"\n      :rules=\"rules\"\n      ref=\"tenantForm\"\n      label-width=\"120px\"\n      class=\"tenant-form-content\"\n    >\n      <el-form-item label=\"租户编码：\" prop=\"tenantCode\">\n        <el-input\n          v-model.trim=\"formData.tenantCode\"\n          placeholder=\"请输入租户编码\"\n          maxlength=\"32\"\n          :disabled=\"isEdit\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"*租户名称：\" prop=\"tenantName\">\n        <el-input\n          v-model.trim=\"formData.tenantName\"\n          placeholder=\"请输入租户名称\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"租户管理员：\" prop=\"tenantAdmin\">\n        <el-input\n          v-model.trim=\"formData.tenantAdmin\"\n          placeholder=\"请输入租户管理员\"\n          maxlength=\"50\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"租户备注：\" prop=\"comments\">\n        <el-input\n          v-model.trim=\"formData.comments\"\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"请输入租户备注\"\n          maxlength=\"200\"\n          show-word-limit\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item label=\"是否选中：\" prop=\"isSelected\">\n        <el-switch\n          v-model=\"formData.isSelected\"\n          active-text=\"是\"\n          inactive-text=\"否\"\n        ></el-switch>\n      </el-form-item>\n    </el-form>\n\n    <div class=\"form-actions\">\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">\n        {{ isEdit ? '保存' : '添加' }}\n      </el-button>\n      <el-button @click=\"handleCancel\">取消</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TenantForm',\n  props: {\n    // 表单数据\n    value: {\n      type: Object,\n      default: () => ({\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      })\n    },\n    // 是否为编辑模式\n    isEdit: {\n      type: Boolean,\n      default: false\n    },\n    // 提交时的loading状态\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      },\n      rules: {\n        tenantCode: [\n          {required: true, message: '请输入租户编码', trigger: 'blur'},\n          {min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur'}\n        ],\n        tenantName: [\n          {required: true, message: '请输入租户名称', trigger: 'blur'},\n          {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}\n        ],\n        tenantAdmin: [\n          {min: 0, max: 50, message: '长度不能超过 50 个字符', trigger: 'blur'}\n        ],\n        comments: [\n          {max: 200, message: '长度不能超过 200 个字符', trigger: 'blur'}\n        ]\n      }\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.formData = {...newVal};\n      },\n      immediate: true,\n      deep: true\n    },\n    formData: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 提交表单\n    handleSubmit() {\n      this.$refs.tenantForm.validate(valid => {\n        if (valid) {\n          this.$emit('submit', this.formData);\n        } else {\n          return false;\n        }\n      });\n    },\n\n    // 取消操作\n    handleCancel() {\n      this.$emit('cancel');\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.tenantForm.resetFields();\n      this.formData = {\n        id: null,\n        tenantCode: '',\n        tenantName: '',\n        tenantAdmin: '',\n        comments: '',\n        isSelected: false\n      };\n    },\n\n    // 验证表单\n    validate() {\n      return new Promise(resolve => {\n        this.$refs.tenantForm.validate(valid => {\n          resolve(valid);\n        });\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.tenant-form {\n  padding: 20px;\n\n  .tenant-form-content {\n    .el-form-item {\n      margin-bottom: 20px;\n\n      .el-input,\n      .el-textarea {\n        width: 100%;\n      }\n    }\n  }\n\n  .form-actions {\n    text-align: center;\n    margin-top: 30px;\n\n    .el-button {\n      margin: 0 10px;\n      min-width: 80px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;AAyEA;EACAA,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;UACAC,EAAA;UACAC,UAAA;UACAC,UAAA;UACAC,WAAA;UACAC,QAAA;UACAC,UAAA;QACA;MAAA;IACA;IACA;IACAC,MAAA;MACAT,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACA;IACAS,OAAA;MACAX,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAV,EAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAM,KAAA;QACAV,UAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,WAAA,GACA;UAAAY,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,QAAA,GACA;UAAAY,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,KAAA;IACArB,KAAA;MACAsB,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAT,QAAA,GAAAU,aAAA,KAAAD,MAAA;MACA;MACAE,SAAA;MACAC,IAAA;IACA;IACAZ,QAAA;MACAQ,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAI,KAAA,UAAAJ,MAAA;MACA;MACAG,IAAA;IACA;EACA;EACAE,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,KAAA,CAAAH,KAAA,WAAAG,KAAA,CAAAhB,QAAA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAqB,YAAA,WAAAA,aAAA;MACA,KAAAR,KAAA;IACA;IAEA;IACAS,SAAA,WAAAA,UAAA;MACA,KAAAL,KAAA,CAAAC,UAAA,CAAAK,WAAA;MACA,KAAAvB,QAAA;QACAV,EAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;MACA;IACA;IAEA;IACAwB,QAAA,WAAAA,SAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACAF,MAAA,CAAAP,KAAA,CAAAC,UAAA,CAAAC,QAAA,WAAAC,KAAA;UACAM,OAAA,CAAAN,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}