import Main from '@/components/main/Main';
// 支付模块的路由

const systemRoutes = [
  {
    name: 'system/cfg',
    path: '/system/cfg',
    component: Main,
    redirect: '/permission',
    meta: {
      showInMenu: true,
      showAlways: true, // 表示一定要展示子菜单
      title: '系统管理',
    },
    children: [
      {
        name: 'systemHome',
        path: '/system/home',
        component: () => import('@/bysc_system/views/home'),
        meta: {
          showInMenu: true,
          path: '/system/home',
          title: '首页',
          icon: 'logo-apple'
        }
      },
      {
        name: 'permission',
        path: '/permission',
        redirect: '/system/user/table',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/permission',
          title: '权限管理',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'userTable',
            path: '/system/user/table',
            component: () => import('@/bysc_system/views/user'),
            meta: {
              showInMenu: true,
              path: '/system/user/table',
              title: '用户管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'roleTable',
            path: '/system/role/table',
            component: () => import('@/bysc_system/views/role'),
            meta: {
              showInMenu: true,
              path: '/system/role/table',
              title: '角色管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'resourceTable',
            path: '/system/resource/table',
            component: () => import('@/bysc_system/views/menus'),
            meta: {
              showInMenu: true,
              path: '/system/resource/table',
              title: '资源管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'organizationTable',
            path: '/system/organization/table',
            component: () => import('@/bysc_system/views/organization'),
            meta: {
              showInMenu: true,
              path: '/system/organization/table',
              title: '组织管理',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'systemBase',
        path: '/system/base',
        redirect: '/system/dict/table',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/base',
          title: '基础配置',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'dictTable',
            path: '/system/dict/table',
            component: () => import('@/bysc_system/views/sysDict'),
            meta: {
              showInMenu: true,
              path: '/system/dict/table',
              title: '系统字典',
              icon: 'logo-apple'
            }
          },

          {
            name: 'tenantTable',
            path: '/base/tenant/table',
            component: () => import('@/bysc_system/views/tenant'),
            meta: {
              showInMenu: true,
              path: '/base/tenant/table',
              title: '租户管理',
              icon: 'logo-apple'
            }
          },
          {
            name: 'config',
            path: '/system/base/config',
            component: () => import('@/bysc_system/views/config'),
            meta: {
              showInMenu: true,
              path: '/system/base/config',
              title: '系统设置',
              icon: 'logo-apple'
            }
          }

        ]
      },
      {
        name: 'systemCode',
        path: '/system/code',
        redirect: '/system/code/generation',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/code',
          title: '代码生成',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'codeGeneration',
            path: '/system/code/generation',
            component: () => import('@/bysc_system/views/codeGeneration'),
            meta: {
              showInMenu: true,
              path: '/system/code/generation',
              title: '代码生成',
              icon: 'logo-apple'
            }
          },
        ]
      },
      {
        name: 'thirdLogin',
        path: '/system/thirdLogin',
        redirect: '/system/thirdLogin/thirdList',
        component: () => import('@/components/main/components/empty-router-view/empty'),
        meta: {
          showInMenu: true,
          path: '/system/thirdLogin',
          title: '第三方登录',
          icon: 'logo-apple'
        },
        children: [
          {
            name: 'thirdList',
            path: '/system/thirdLogin/thirdList',
            component: () => import('@/bysc_system/views/thirdLogin'),
            meta: {
              showInMenu: true,
              path: '/system/thirdLogin/thirdList',
              title: '第三方列表',
              icon: 'logo-apple'
            }
          },
        ]
      },





    ]
  }
];

export default systemRoutes;
