import localCache from '@/utils/storage';
function checkArray(key) {
  let arr = localCache.getLocal('btnPermissions') || [];
  let index = arr.indexOf(key);
  if (index > -1) {
    return true; // 有权限
  }
  return false; // 无权限

}
const permission = {
  inserted: function (el, binding) {
    let permission = binding.value; // 获取到 v-permission的值
    if (permission) {
      let hasPermission = checkArray(permission);
      if (!hasPermission) {
        // 没有权限 移除Dom元素
        el.parentNode && el.parentNode.removeChild(el);
        // 将移除dom改为隐藏dom
        // if (el.parentNode) {
        //   el.style.visibility= "hidden"
        // }
      }
    }
  },
};

export default permission;
